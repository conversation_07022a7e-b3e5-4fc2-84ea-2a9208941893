#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
📐 图片分辨率修改模块 📐
功能: 批量修改图片分辨率，支持自定义分辨率和多种缩放模式
"""

import os
import logging
import time
from PIL import Image, ImageOps
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QLineEdit, QFileDialog, QProgressBar, 
                            QMessageBox, QCheckBox, QApplication, QSpinBox,
                            QComboBox, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex, QWaitCondition

class ImageResizeThread(QThread):
    """图片分辨率修改线程"""
    progress_signal = pyqtSignal(int)
    status_signal = pyqtSignal(str)
    complete_signal = pyqtSignal(bool, str)
    
    def __init__(self, image_folder, width, height, keep_aspect_ratio=True, 
                 keep_original=False, resize_mode="fit"):
        super().__init__()
        self.image_folder = image_folder
        self.width = width
        self.height = height
        self.keep_aspect_ratio = keep_aspect_ratio
        self.keep_original = keep_original
        self.resize_mode = resize_mode
        self.is_running = True
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
        self.condition.wakeAll()
        
    def run(self):
        try:
            # 获取文件夹中的所有图片文件
            self.status_signal.emit("正在扫描图片文件...")
            image_files = []
            supported_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp']
            
            # 检查是否被中断
            if not self.is_running:
                return
                
            for filename in os.listdir(self.image_folder):
                file_path = os.path.join(self.image_folder, filename)
                if os.path.isfile(file_path) and os.path.splitext(filename)[1].lower() in supported_formats:
                    image_files.append(filename)
            
            # 如果没有图片文件，则返回错误
            if not image_files:
                self.complete_signal.emit(False, "所选文件夹中没有支持的图片文件")
                return
            
            # 如果需要保留原图，创建输出文件夹
            output_folder = self.image_folder
            if self.keep_original:
                output_folder = os.path.join(self.image_folder, f"已调整分辨率_{self.width}x{self.height}")
                try:
                    if not os.path.exists(output_folder):
                        os.makedirs(output_folder)
                        self.status_signal.emit(f"已创建输出文件夹: {os.path.basename(output_folder)}")
                except Exception as e:
                    self.complete_signal.emit(False, f"创建输出文件夹失败: {str(e)}")
                    return

            # 处理每个图片文件
            processed_files = 0
            total_files = len(image_files)
            last_update_time = time.time()

            self.status_signal.emit(f"开始处理 {total_files} 个图片文件...")

            for filename in image_files:
                # 检查是否被中断
                if not self.is_running:
                    self.status_signal.emit("处理已取消")
                    return
                    
                file_path = os.path.join(self.image_folder, filename)
                
                try:
                    # 打开图片
                    with Image.open(file_path) as img:
                        original_size = img.size
                        
                        # 根据缩放模式处理图片
                        if self.resize_mode == "stretch":
                            # 拉伸模式：直接调整到目标尺寸
                            resized_img = img.resize((self.width, self.height), Image.Resampling.LANCZOS)
                        elif self.resize_mode == "fit":
                            # 适应模式：保持比例，适应到目标尺寸内
                            resized_img = ImageOps.fit(img, (self.width, self.height), Image.Resampling.LANCZOS)
                        elif self.resize_mode == "pad":
                            # 填充模式：保持比例，用白色填充空白区域
                            resized_img = ImageOps.pad(img, (self.width, self.height), Image.Resampling.LANCZOS, color='white')
                        else:
                            # 默认使用适应模式
                            resized_img = ImageOps.fit(img, (self.width, self.height), Image.Resampling.LANCZOS)
                        
                        # 保存处理后的图片
                        if self.keep_original:
                            # 保留原图，保存到子文件夹，文件名保持不变
                            new_file_path = os.path.join(output_folder, filename)
                            resized_img.save(new_file_path, quality=95)
                        else:
                            # 覆盖原图
                            resized_img.save(file_path, quality=95)
                        
                        self.status_signal.emit(f"已处理: {filename} ({original_size[0]}x{original_size[1]} → {self.width}x{self.height})")
                    
                except Exception as e:
                    self.status_signal.emit(f"处理图片 {filename} 时出错: {str(e)}")
                
                # 更新进度
                processed_files += 1
                
                # 限制进度更新频率，避免UI卡顿
                current_time = time.time()
                if current_time - last_update_time > 0.1 or processed_files == total_files:
                    progress_percent = int(processed_files * 100 / total_files)
                    self.progress_signal.emit(progress_percent)
                    last_update_time = current_time
                    # 处理事件，确保UI响应
                    QApplication.processEvents()
            
            complete_message = f"图片分辨率修改完成！共处理 {processed_files} 个图片文件，目标分辨率：{self.width}x{self.height}"
            self.status_signal.emit(complete_message)
            self.complete_signal.emit(True, complete_message)
            
        except Exception as e:
            error_message = f"图片处理过程中出错: {str(e)}"
            self.status_signal.emit(error_message)
            self.complete_signal.emit(False, error_message)

class ImageResizer(QWidget):
    """图片分辨率修改功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.worker_thread = None
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能可以批量修改图片分辨率，支持多种缩放模式和自定义分辨率")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(10)
        
        # 图片文件夹选择
        image_folder_layout = QHBoxLayout()
        image_folder_label = QLabel("选择图片文件夹:")
        self.image_folder_input = QLineEdit()
        self.image_folder_input.setReadOnly(True)
        image_folder_button = QPushButton("浏览...")
        image_folder_button.clicked.connect(self.select_image_folder)
        
        image_folder_layout.addWidget(image_folder_label)
        image_folder_layout.addWidget(self.image_folder_input)
        image_folder_layout.addWidget(image_folder_button)
        form_layout.addLayout(image_folder_layout)
        
        # 分辨率设置组
        resolution_group = QGroupBox("分辨率设置")
        resolution_layout = QGridLayout(resolution_group)
        
        # 宽度设置
        resolution_layout.addWidget(QLabel("宽度:"), 0, 0)
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setRange(1, 10000)
        self.width_spinbox.setValue(1920)
        self.width_spinbox.setSuffix(" px")
        self.width_spinbox.valueChanged.connect(self.save_config)
        resolution_layout.addWidget(self.width_spinbox, 0, 1)
        
        # 高度设置
        resolution_layout.addWidget(QLabel("高度:"), 0, 2)
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(1, 10000)
        self.height_spinbox.setValue(1080)
        self.height_spinbox.setSuffix(" px")
        self.height_spinbox.valueChanged.connect(self.save_config)
        resolution_layout.addWidget(self.height_spinbox, 0, 3)
        
        # 常用分辨率快捷按钮
        common_resolutions = [
            ("1920x1080", 1920, 1080),
            ("1280x720", 1280, 720),
            ("1024x768", 1024, 768),
            ("3709x1919", 3709, 1919)
        ]
        
        resolution_layout.addWidget(QLabel("常用分辨率:"), 1, 0)
        for i, (name, w, h) in enumerate(common_resolutions):
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, width=w, height=h: self.set_resolution(width, height))
            resolution_layout.addWidget(btn, 1, i + 1)
        
        form_layout.addWidget(resolution_group)
        
        # 缩放模式设置
        mode_group = QGroupBox("缩放模式")
        mode_layout = QVBoxLayout(mode_group)
        
        self.resize_mode_combo = QComboBox()
        self.resize_mode_combo.addItem("适应模式 (保持比例，裁剪多余部分)", "fit")
        self.resize_mode_combo.addItem("填充模式 (保持比例，用白色填充)", "pad")
        self.resize_mode_combo.addItem("拉伸模式 (可能变形)", "stretch")
        self.resize_mode_combo.currentTextChanged.connect(self.save_config)
        mode_layout.addWidget(self.resize_mode_combo)
        
        form_layout.addWidget(mode_group)
        
        # 选项设置
        options_layout = QVBoxLayout()
        
        # 保留原图选项
        self.keep_original_checkbox = QCheckBox("保留原图（处理后的图片将保存到子文件夹中）")
        self.keep_original_checkbox.stateChanged.connect(self.save_config)
        options_layout.addWidget(self.keep_original_checkbox)
        
        form_layout.addLayout(options_layout)
        main_layout.addLayout(form_layout)
        
        # 添加状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        
        # 添加开始按钮
        self.start_button = QPushButton("开始处理")
        self.start_button.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_button)
        
        # 添加取消按钮
        self.cancel_button = QPushButton("取消处理")
        self.cancel_button.clicked.connect(self.cancel_processing)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)

    def set_resolution(self, width, height):
        """设置分辨率"""
        self.width_spinbox.setValue(width)
        self.height_spinbox.setValue(height)
        self.save_config()

    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('image_resizer', {})

            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.image_folder_input.setText(last_folder)

            # 设置分辨率
            width = config.get('width', 1920)
            height = config.get('height', 1080)
            self.width_spinbox.setValue(width)
            self.height_spinbox.setValue(height)

            # 设置是否保留原图
            keep_original = config.get('keep_original', False)
            self.keep_original_checkbox.setChecked(keep_original)

            # 设置缩放模式
            resize_mode = config.get('resize_mode', 'fit')
            for i in range(self.resize_mode_combo.count()):
                if self.resize_mode_combo.itemData(i) == resize_mode:
                    self.resize_mode_combo.setCurrentIndex(i)
                    break

    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'image_resizer' not in self.parent.config:
                self.parent.config['image_resizer'] = {}

            # 保存当前设置
            self.parent.config['image_resizer']['last_folder'] = self.image_folder_input.text()
            self.parent.config['image_resizer']['width'] = self.width_spinbox.value()
            self.parent.config['image_resizer']['height'] = self.height_spinbox.value()
            self.parent.config['image_resizer']['keep_original'] = self.keep_original_checkbox.isChecked()

            # 保存缩放模式
            current_index = self.resize_mode_combo.currentIndex()
            if current_index >= 0:
                mode_data = self.resize_mode_combo.itemData(current_index)
                if mode_data:
                    self.parent.config['image_resizer']['resize_mode'] = mode_data

    def select_image_folder(self):
        """选择图片文件夹"""
        # 从上次的文件夹开始浏览
        start_dir = self.image_folder_input.text() if self.image_folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹", start_dir)
        if folder_path:
            self.image_folder_input.setText(folder_path)
            logging.info(f"已选择图片文件夹: {folder_path}")
            # 保存当前文件夹路径
            self.save_config()

    def start_processing(self):
        """开始图片处理"""
        # 获取输入参数
        image_folder = self.image_folder_input.text()
        width = self.width_spinbox.value()
        height = self.height_spinbox.value()
        keep_original = self.keep_original_checkbox.isChecked()

        # 获取缩放模式
        current_index = self.resize_mode_combo.currentIndex()
        resize_mode = "fit"  # 默认值
        if current_index >= 0:
            mode_data = self.resize_mode_combo.itemData(current_index)
            if mode_data:
                resize_mode = mode_data

        # 验证输入
        if not image_folder:
            QMessageBox.warning(self, "警告", "请选择图片文件夹")
            logging.warning("未选择图片文件夹")
            return

        if width <= 0 or height <= 0:
            QMessageBox.warning(self, "警告", "请设置有效的分辨率")
            logging.warning("分辨率设置无效")
            return

        # 保存当前配置
        self.save_config()

        # 重置状态和进度条
        self.status_label.setText("准备处理图片...")
        self.progress_bar.setValue(0)

        # 禁用开始按钮，启用取消按钮
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 创建并启动工作线程
        self.worker_thread = ImageResizeThread(
            image_folder, width, height, True, keep_original, resize_mode
        )
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.status_signal.connect(self.update_status)
        self.worker_thread.complete_signal.connect(self.handle_complete)
        self.worker_thread.start()

        logging.info(f"开始处理图片: 文件夹={image_folder}, 分辨率={width}x{height}, 模式={resize_mode}, 保留原图={keep_original}")

    def cancel_processing(self):
        """取消图片处理"""
        if self.worker_thread and self.worker_thread.isRunning():
            # 停止工作线程
            self.worker_thread.stop()
            # 等待线程结束
            self.worker_thread.wait()
            # 更新状态
            self.status_label.setText("处理已取消")
            # 启用开始按钮，禁用取消按钮
            self.start_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            logging.info("图片分辨率修改已取消")

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)

    def handle_complete(self, success, message):
        """处理完成回调"""
        # 重置进度条
        self.progress_bar.setValue(100 if success else 0)

        # 更新状态
        self.status_label.setText(message)

        # 启用开始按钮，禁用取消按钮
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)

        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.critical(self, "错误", message)

    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.worker_thread.wait()
        event.accept()

# 为了兼容性，保留原来的类名
图片分辨率修改 = ImageResizer
图片分辨率修改线程 = ImageResizeThread

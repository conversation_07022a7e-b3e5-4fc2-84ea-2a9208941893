#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
✨ 图片分类模块 ✨
功能：自动将图片按照横竖方向分类到不同文件夹
"""

import os
import shutil
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QFileDialog, QCheckBox, QSpinBox,
                            QProgressBar, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

from PIL import Image

class ImageClassifierThread(QThread):
    """图片分类线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    status_updated = pyqtSignal(str, str)  # 消息, 级别
    completed = pyqtSignal()
    
    def __init__(self, folder_path, diff_threshold):
        super().__init__()
        self.folder_path = folder_path
        self.diff_threshold = diff_threshold
        self.is_running = True
        
    def run(self):
        """执行图片分类"""
        try:
            # 创建目标子文件夹
            vertical_dir = os.path.join(self.folder_path, "竖图")
            horizontal_dir = os.path.join(self.folder_path, "横图")
            os.makedirs(vertical_dir, exist_ok=True)
            os.makedirs(horizontal_dir, exist_ok=True)
            
            # 支持的图片格式
            img_exts = ('.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif')
            
            # 获取所有图片文件
            image_files = []
            for filename in os.listdir(self.folder_path):
                file_path = os.path.join(self.folder_path, filename)
                
                # 跳过子文件夹
                if os.path.isdir(file_path):
                    continue
                    
                # 检查图片格式
                if filename.lower().endswith(img_exts):
                    image_files.append((filename, file_path))
            
            total_files = len(image_files)
            self.status_updated.emit(f"找到 {total_files} 个图片文件", "INFO")
            
            # 处理每个图片
            for index, (filename, file_path) in enumerate(image_files):
                if not self.is_running:
                    break
                    
                try:
                    # 获取图片尺寸
                    with Image.open(file_path) as img:
                        width, height = img.size
                    
                    # 判断横竖图
                    if width - height >= self.diff_threshold:
                        target_dir = horizontal_dir
                        orientation = "横图"
                    else:
                        target_dir = vertical_dir
                        orientation = "竖图"
                    
                    # 移动文件
                    dest_path = os.path.join(target_dir, filename)
                    shutil.move(file_path, dest_path)
                    
                    # 更新状态
                    self.status_updated.emit(f"已处理: {filename} ({width}x{height}, {orientation})", "INFO")
                    
                except Exception as e:
                    self.status_updated.emit(f"处理文件 {filename} 时出错: {str(e)}", "ERROR")
                
                # 更新进度
                self.progress_updated.emit(index + 1, total_files)
            
            self.status_updated.emit(f"分类完成！共处理 {total_files} 个文件", "SUCCESS")
            self.completed.emit()
            
        except Exception as e:
            self.status_updated.emit(f"发生错误: {str(e)}", "ERROR")
    
    def stop(self):
        """停止线程"""
        self.is_running = False

class ImageClassifier(QWidget):
    """图片分类界面类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.classifier_thread = None
        self.init_ui()
        
        # 加载配置
        if parent and hasattr(parent, 'config'):
            self.load_config()
        
    def init_ui(self):
        """初始化界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 说明文本
        description = QLabel("此功能可以自动将图片按照横竖方向分类到不同文件夹。")
        description.setWordWrap(True)
        description.setStyleSheet("font-size: 11pt; margin-bottom: 10px;")
        layout.addWidget(description)
        
        # 参数设置组
        settings_group = QGroupBox("参数设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_label = QLabel("宽高差阈值:")
        self.threshold_spinbox = QSpinBox()
        self.threshold_spinbox.setRange(1, 1000)
        self.threshold_spinbox.setValue(200)
        self.threshold_spinbox.setSuffix(" 像素")
        self.threshold_spinbox.setToolTip("当图片宽度减去高度大于此值时，判定为横图")
        self.threshold_spinbox.valueChanged.connect(self.save_config)
        threshold_layout.addWidget(threshold_label)
        threshold_layout.addWidget(self.threshold_spinbox)
        threshold_layout.addStretch()
        settings_layout.addLayout(threshold_layout)
        
        layout.addWidget(settings_group)
        
        # 选择文件夹
        folder_layout = QHBoxLayout()
        self.folder_label = QLabel("未选择文件夹")
        self.folder_label.setStyleSheet("color: #666;")
        select_button = QPushButton("选择图片文件夹")
        select_button.clicked.connect(self.select_folder)
        folder_layout.addWidget(self.folder_label, 1)
        folder_layout.addWidget(select_button)
        layout.addLayout(folder_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        self.start_button = QPushButton("开始分类")
        self.start_button.setEnabled(False)
        self.start_button.clicked.connect(self.start_classification)
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_classification)
        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.stop_button)
        layout.addLayout(buttons_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
    def load_config(self):
        """从父窗口加载配置"""
        try:
            if 'image_classifier' in self.parent.config:
                config = self.parent.config['image_classifier']
                
                # 加载阈值
                if 'threshold' in config:
                    self.threshold_spinbox.setValue(config['threshold'])
                
                # 加载上次使用的文件夹
                if 'last_folder' in config and config['last_folder']:
                    folder_path = config['last_folder']
                    if os.path.isdir(folder_path):
                        self.folder_label.setText(folder_path)
                        self.folder_label.setStyleSheet("color: #000;")
                        self.start_button.setEnabled(True)
        except Exception as e:
            logging.error(f"加载图片分类配置出错: {str(e)}")
    
    def save_config(self):
        """保存配置到父窗口"""
        if self.parent and hasattr(self.parent, 'config'):
            try:
                # 确保配置字典中有image_classifier键
                if 'image_classifier' not in self.parent.config:
                    self.parent.config['image_classifier'] = {}
                
                # 保存阈值
                self.parent.config['image_classifier']['threshold'] = self.threshold_spinbox.value()
                
                # 保存文件夹路径（如果已选择）
                folder_path = self.folder_label.text()
                if folder_path != "未选择文件夹":
                    self.parent.config['image_classifier']['last_folder'] = folder_path
            except Exception as e:
                logging.error(f"保存图片分类配置出错: {str(e)}")
        
    def select_folder(self):
        """选择图片文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
        if folder_path:
            self.folder_label.setText(folder_path)
            self.folder_label.setStyleSheet("color: #000;")
            self.start_button.setEnabled(True)
            logging.info(f"已选择文件夹: {folder_path}")
            # 保存配置
            self.save_config()
    
    def start_classification(self):
        """开始分类图片"""
        folder_path = self.folder_label.text()
        if not os.path.isdir(folder_path):
            QMessageBox.warning(self, "警告", "请先选择有效的图片文件夹")
            return
        
        diff_threshold = self.threshold_spinbox.value()
        # 保存配置
        self.save_config()
        
        # 禁用开始按钮，启用停止按钮
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        
        # 创建并启动线程
        self.classifier_thread = ImageClassifierThread(folder_path, diff_threshold)
        self.classifier_thread.progress_updated.connect(self.update_progress)
        self.classifier_thread.status_updated.connect(self.update_status)
        self.classifier_thread.completed.connect(self.on_classification_completed)
        self.classifier_thread.start()
        
        logging.info(f"开始分类图片，宽高差阈值: {diff_threshold}像素")
    
    def stop_classification(self):
        """停止分类"""
        if self.classifier_thread and self.classifier_thread.isRunning():
            self.classifier_thread.stop()
            self.classifier_thread.wait()
            logging.warning("图片分类已手动停止")
            
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
    
    def update_status(self, message, level):
        """更新状态信息"""
        if level == "INFO":
            logging.info(message)
        elif level == "WARNING":
            logging.warning(message)
        elif level == "ERROR":
            logging.error(message)
        elif level == "SUCCESS":
            logging.info(message)
    
    def on_classification_completed(self):
        """分类完成后的处理"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

# 用于测试模块
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = ImageClassifier()
    window.show()
    sys.exit(app.exec_()) 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🚀 程序快捷启动模块 🚀
功能: 保存常用程序路径，一键快速启动
"""

import os
import sys
import json
import logging
import subprocess
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QLineEdit, QFileDialog, QMessageBox, 
                            QScrollArea, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont

class ProgramLauncher(QWidget):
    """程序快捷启动功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.program_slots = []  # 存储程序槽位
        self.max_slots = 10      # 最大槽位数
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("添加常用程序的快捷方式，点击即可快速启动")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 创建滚动区域的内容部件
        scroll_content = QWidget()
        self.slots_layout = QVBoxLayout(scroll_content)
        self.slots_layout.setSpacing(10)
        
        # 创建程序槽位
        for i in range(self.max_slots):
            slot_layout = self.create_program_slot(i)
            self.slots_layout.addLayout(slot_layout)
            
        # 添加弹性空间
        self.slots_layout.addStretch()
        
        # 设置滚动区域的部件
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        
        self.setLayout(main_layout)
    
    def create_program_slot(self, index):
        """创建单个程序槽位"""
        slot_layout = QHBoxLayout()
        
        # 创建槽位标签
        slot_label = QLabel(f"程序 {index + 1}:")
        slot_label.setMinimumWidth(60)
        
        # 创建路径输入框
        path_input = QLineEdit()
        path_input.setReadOnly(True)
        path_input.textChanged.connect(self.save_config)  # 添加文本变化事件
        path_input.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        path_input.setPlaceholderText("点击浏览选择程序...")
        
        # 创建浏览按钮
        browse_button = QPushButton("浏览...")
        browse_button.setMaximumWidth(80)
        browse_button.clicked.connect(lambda: self.select_program(index))
        
        # 创建启动按钮
        launch_button = QPushButton("启动")
        launch_button.setMaximumWidth(80)
        launch_button.clicked.connect(lambda: self.launch_program(index))
        launch_button.setEnabled(False)  # 初始禁用
        
        # 创建清除按钮
        clear_button = QPushButton("清除")
        clear_button.setMaximumWidth(80)
        clear_button.clicked.connect(lambda: self.clear_program(index))
        clear_button.setEnabled(False)  # 初始禁用
        
        # 添加到布局
        slot_layout.addWidget(slot_label)
        slot_layout.addWidget(path_input)
        slot_layout.addWidget(browse_button)
        slot_layout.addWidget(launch_button)
        slot_layout.addWidget(clear_button)
        
        # 保存槽位信息
        self.program_slots.append({
            'path_input': path_input,
            'launch_button': launch_button,
            'clear_button': clear_button,
            'path': '',
            'name': ''
        })
        
        return slot_layout
    
    def select_program(self, index):
        """选择程序文件"""
        # 获取上次的路径作为起始目录
        start_dir = os.path.dirname(self.program_slots[index]['path']) if self.program_slots[index]['path'] else ""
        
        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择程序", 
            start_dir,
            "可执行文件 (*.exe);;所有文件 (*.*)"
        )
        
        if file_path:
            # 获取程序名称
            program_name = os.path.basename(file_path)
            
            # 更新槽位信息
            self.program_slots[index]['path'] = file_path
            self.program_slots[index]['name'] = program_name
            self.program_slots[index]['path_input'].setText(file_path)
            self.program_slots[index]['launch_button'].setEnabled(True)
            self.program_slots[index]['clear_button'].setEnabled(True)
            
            # 保存配置
            self.save_config()
            
            logging.info(f"已添加程序: {program_name}")
    
    def launch_program(self, index):
        """启动程序"""
        program_path = self.program_slots[index]['path']
        program_name = self.program_slots[index]['name']
        
        if not program_path or not os.path.exists(program_path):
            QMessageBox.warning(self, "警告", f"程序路径无效: {program_path}")
            return
        
        try:
            # 获取程序所在目录作为工作目录
            program_dir = os.path.dirname(program_path)
            
            # 使用subprocess启动程序，并设置工作目录
            subprocess.Popen(program_path, cwd=program_dir)
            logging.info(f"已启动程序: {program_name}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法启动程序: {str(e)}")
            logging.error(f"启动程序失败: {program_name}, 错误: {str(e)}")
    
    def clear_program(self, index):
        """清除程序槽位"""
        # 清除槽位信息
        self.program_slots[index]['path'] = ''
        self.program_slots[index]['name'] = ''
        self.program_slots[index]['path_input'].setText('')
        self.program_slots[index]['launch_button'].setEnabled(False)
        self.program_slots[index]['clear_button'].setEnabled(False)
        
        # 保存配置
        self.save_config()
        
        logging.info(f"已清除程序槽位 {index + 1}")
    
    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('program_launcher', {})
            
            # 加载程序槽位
            programs = config.get('programs', [])
            for i, program in enumerate(programs):
                if i < self.max_slots:
                    self.program_slots[i]['path'] = program.get('path', '')
                    self.program_slots[i]['name'] = program.get('name', '')
                    
                    if self.program_slots[i]['path']:
                        self.program_slots[i]['path_input'].setText(self.program_slots[i]['path'])
                        self.program_slots[i]['launch_button'].setEnabled(True)
                        self.program_slots[i]['clear_button'].setEnabled(True)
    
    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'program_launcher' not in self.parent.config:
                self.parent.config['program_launcher'] = {}
            
            # 保存程序槽位
            programs = []
            for slot in self.program_slots:
                programs.append({
                    'path': slot['path'],
                    'name': slot['name']
                })
            
            self.parent.config['program_launcher']['programs'] = programs
            
            # 立即保存配置
            if hasattr(self.parent, 'save_config'):
                self.parent.save_config()

# 为了兼容性，提供中文类名
程序快捷启动 = ProgramLauncher 
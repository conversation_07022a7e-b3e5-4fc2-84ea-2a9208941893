#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔍 图片筛选模块 🔍
功能：根据条件筛选和删除图片文件
"""

import os
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QFileDialog, QCheckBox, QProgressBar, 
                            QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

from PIL import Image

class ImageFilterThread(QThread):
    """图片筛选线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    status_updated = pyqtSignal(str, str)  # 消息, 级别
    completed = pyqtSignal()
    
    def __init__(self, folder_path, delete_videos, delete_horizontal, delete_vertical):
        super().__init__()
        self.folder_path = folder_path
        self.delete_videos = delete_videos
        self.delete_horizontal = delete_horizontal
        self.delete_vertical = delete_vertical
        self.is_running = True
        
    def run(self):
        """执行图片筛选"""
        try:
            # 支持的图片格式
            img_exts = ('.jpg', '.jpeg', '.png', '.webp', '.bmp', '.gif', '.tiff', '.tif')
            # 支持的视频格式
            video_exts = ('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp', '.mpg', '.mpeg')
            
            # 获取所有文件
            all_files = []
            for filename in os.listdir(self.folder_path):
                file_path = os.path.join(self.folder_path, filename)
                
                # 跳过子文件夹
                if os.path.isdir(file_path):
                    continue
                    
                all_files.append((filename, file_path))
            
            total_files = len(all_files)
            self.status_updated.emit(f"找到 {total_files} 个文件", "INFO")
            
            deleted_count = 0
            
            # 处理每个文件
            for index, (filename, file_path) in enumerate(all_files):
                if not self.is_running:
                    break
                    
                try:
                    file_ext = filename.lower()
                    should_delete = False
                    delete_reason = ""
                    
                    # 检查是否为视频文件
                    if self.delete_videos and file_ext.endswith(video_exts):
                        should_delete = True
                        delete_reason = "视频文件"
                    
                    # 检查是否为图片文件，并判断横竖图
                    elif file_ext.endswith(img_exts):
                        try:
                            with Image.open(file_path) as img:
                                width, height = img.size
                                
                                # 判断横竖图（竖图标准放宽松）
                                if self.delete_horizontal and width > height * 1.2:  # 宽度明显大于高度才算横图
                                    should_delete = True
                                    delete_reason = f"横图 ({width}x{height})"
                                elif self.delete_vertical and height > width * 1.5:  # 高度要明显大于宽度才算竖图
                                    should_delete = True
                                    delete_reason = f"竖图 ({width}x{height})"
                                    
                        except Exception as e:
                            self.status_updated.emit(f"无法读取图片 {filename}: {str(e)}", "WARNING")
                    
                    # 执行删除操作
                    if should_delete:
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                            self.status_updated.emit(f"已删除: {filename} ({delete_reason})", "INFO")
                        except Exception as e:
                            self.status_updated.emit(f"删除文件 {filename} 失败: {str(e)}", "ERROR")
                    
                except Exception as e:
                    self.status_updated.emit(f"处理文件 {filename} 时出错: {str(e)}", "ERROR")
                
                # 更新进度
                self.progress_updated.emit(index + 1, total_files)
            
            self.status_updated.emit(f"筛选完成！共处理 {total_files} 个文件，删除 {deleted_count} 个文件", "SUCCESS")
            self.completed.emit()
            
        except Exception as e:
            self.status_updated.emit(f"发生错误: {str(e)}", "ERROR")
    
    def stop(self):
        """停止线程"""
        self.is_running = False

class ImageFilter(QWidget):
    """图片筛选界面类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.filter_thread = None
        self.init_ui()
        
        # 加载配置
        if parent and hasattr(parent, 'config'):
            self.load_config()
        
    def init_ui(self):
        """初始化界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # 说明文本
        description = QLabel("此功能可以根据设定的条件筛选和删除文件。请谨慎使用，删除的文件无法恢复。")
        description.setWordWrap(True)
        description.setStyleSheet("font-size: 11pt; margin-bottom: 10px;")
        layout.addWidget(description)
        
        # 筛选条件组
        filter_group = QGroupBox("筛选条件")
        filter_layout = QVBoxLayout(filter_group)
        
        # 删除视频文件选项
        self.delete_videos_cb = QCheckBox("删除视频文件")
        self.delete_videos_cb.setToolTip("删除常见格式的视频文件（mp4, avi, mov等）")
        self.delete_videos_cb.stateChanged.connect(self.save_config)
        filter_layout.addWidget(self.delete_videos_cb)
        
        # 删除横图选项
        self.delete_horizontal_cb = QCheckBox("删除比例为横图")
        self.delete_horizontal_cb.setToolTip("删除宽度明显大于高度的图片（宽度 > 高度 × 1.2）")
        self.delete_horizontal_cb.stateChanged.connect(self.save_config)
        filter_layout.addWidget(self.delete_horizontal_cb)
        
        # 删除竖图选项
        self.delete_vertical_cb = QCheckBox("删除比例为竖图")
        self.delete_vertical_cb.setToolTip("删除高度明显大于宽度的图片（高度 > 宽度 × 1.5，标准较宽松）")
        self.delete_vertical_cb.stateChanged.connect(self.save_config)
        filter_layout.addWidget(self.delete_vertical_cb)
        
        layout.addWidget(filter_group)
        
        # 选择文件夹
        folder_layout = QHBoxLayout()
        self.folder_label = QLabel("未选择文件夹")
        self.folder_label.setStyleSheet("color: #666;")
        select_button = QPushButton("选择文件夹")
        select_button.clicked.connect(self.select_folder)
        folder_layout.addWidget(self.folder_label, 1)
        folder_layout.addWidget(select_button)
        layout.addLayout(folder_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        layout.addWidget(self.progress_bar)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        self.start_button = QPushButton("开始筛选")
        self.start_button.setEnabled(False)
        self.start_button.clicked.connect(self.start_filtering)
        self.stop_button = QPushButton("停止")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_filtering)
        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.stop_button)
        layout.addLayout(buttons_layout)
        
        # 添加弹性空间
        layout.addStretch()

    def load_config(self):
        """从父窗口加载配置"""
        try:
            if 'image_filter' in self.parent.config:
                config = self.parent.config['image_filter']

                # 加载筛选选项
                if 'delete_videos' in config:
                    self.delete_videos_cb.setChecked(config['delete_videos'])
                if 'delete_horizontal' in config:
                    self.delete_horizontal_cb.setChecked(config['delete_horizontal'])
                if 'delete_vertical' in config:
                    self.delete_vertical_cb.setChecked(config['delete_vertical'])

                # 加载上次使用的文件夹
                if 'last_folder' in config and config['last_folder']:
                    folder_path = config['last_folder']
                    if os.path.isdir(folder_path):
                        self.folder_label.setText(folder_path)
                        self.folder_label.setStyleSheet("color: #000;")
                        self.start_button.setEnabled(True)
        except Exception as e:
            logging.error(f"加载图片筛选配置出错: {str(e)}")

    def save_config(self):
        """保存配置到父窗口"""
        if self.parent and hasattr(self.parent, 'config'):
            try:
                # 确保配置字典中有image_filter键
                if 'image_filter' not in self.parent.config:
                    self.parent.config['image_filter'] = {}

                # 保存筛选选项
                self.parent.config['image_filter']['delete_videos'] = self.delete_videos_cb.isChecked()
                self.parent.config['image_filter']['delete_horizontal'] = self.delete_horizontal_cb.isChecked()
                self.parent.config['image_filter']['delete_vertical'] = self.delete_vertical_cb.isChecked()

                # 保存文件夹路径（如果已选择）
                folder_path = self.folder_label.text()
                if folder_path != "未选择文件夹":
                    self.parent.config['image_filter']['last_folder'] = folder_path
            except Exception as e:
                logging.error(f"保存图片筛选配置出错: {str(e)}")

    def select_folder(self):
        """选择文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择要筛选的文件夹")
        if folder_path:
            self.folder_label.setText(folder_path)
            self.folder_label.setStyleSheet("color: #000;")
            self.start_button.setEnabled(True)
            logging.info(f"已选择文件夹: {folder_path}")
            # 保存配置
            self.save_config()

    def start_filtering(self):
        """开始筛选文件"""
        folder_path = self.folder_label.text()
        if not os.path.isdir(folder_path):
            QMessageBox.warning(self, "警告", "请先选择有效的文件夹")
            return

        # 检查是否至少选择了一个筛选条件
        if not (self.delete_videos_cb.isChecked() or
                self.delete_horizontal_cb.isChecked() or
                self.delete_vertical_cb.isChecked()):
            QMessageBox.warning(self, "警告", "请至少选择一个筛选条件")
            return

        # 确认对话框
        conditions = []
        if self.delete_videos_cb.isChecked():
            conditions.append("删除视频文件")
        if self.delete_horizontal_cb.isChecked():
            conditions.append("删除横图")
        if self.delete_vertical_cb.isChecked():
            conditions.append("删除竖图")

        condition_text = "、".join(conditions)
        reply = QMessageBox.question(
            self, "确认操作",
            f"即将执行以下操作：{condition_text}\n\n"
            f"目标文件夹：{folder_path}\n\n"
            "删除的文件无法恢复，确定要继续吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 保存配置
        self.save_config()

        # 禁用开始按钮，启用停止按钮
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 创建并启动线程
        self.filter_thread = ImageFilterThread(
            folder_path,
            self.delete_videos_cb.isChecked(),
            self.delete_horizontal_cb.isChecked(),
            self.delete_vertical_cb.isChecked()
        )
        self.filter_thread.progress_updated.connect(self.update_progress)
        self.filter_thread.status_updated.connect(self.update_status)
        self.filter_thread.completed.connect(self.on_filtering_completed)
        self.filter_thread.start()

        logging.info(f"开始筛选文件，条件：{condition_text}")

    def stop_filtering(self):
        """停止筛选"""
        if self.filter_thread and self.filter_thread.isRunning():
            self.filter_thread.stop()
            self.filter_thread.wait()
            logging.warning("图片筛选已手动停止")

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, message, level):
        """更新状态信息"""
        if level == "INFO":
            logging.info(message)
        elif level == "WARNING":
            logging.warning(message)
        elif level == "ERROR":
            logging.error(message)
        elif level == "SUCCESS":
            logging.info(message)

    def on_filtering_completed(self):
        """筛选完成后的处理"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

# 用于测试模块
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    window = ImageFilter()
    window.show()
    sys.exit(app.exec_())

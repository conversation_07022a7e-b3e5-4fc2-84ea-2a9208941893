#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔍 重复图删除模块 🔍
功能: 使用最高效的方法检测和删除重复图片，支持多种对比算法
"""

import os
import logging
import hashlib
import time
from collections import defaultdict
from PIL import Image
# import imagehash  # 暂时注释掉，使用自定义实现
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QFileDialog, QProgressBar, QMessageBox,
                            QCheckBox, QApplication, QComboBox, QSpinBox,
                            QGroupBox, QTextEdit, QSplitter, QLineEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex, QWaitCondition

class DuplicateDetectionThread(QThread):
    """重复图片检测线程"""
    progress_signal = pyqtSignal(int, int)  # 当前进度, 总数
    status_signal = pyqtSignal(str)
    duplicate_found_signal = pyqtSignal(list)  # 发现的重复组
    complete_signal = pyqtSignal(bool, str, int, int)  # 成功, 消息, 总文件数, 重复文件数
    
    def __init__(self, folder_path, comparison_method="hash", similarity_threshold=95):
        super().__init__()
        self.folder_path = folder_path
        self.comparison_method = comparison_method
        self.similarity_threshold = similarity_threshold
        self.is_running = True
        self.mutex = QMutex()
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
        
    def get_file_hash(self, filepath):
        """获取文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return None
    
    def get_image_hash(self, filepath, hash_type="phash"):
        """获取图片感知哈希值"""
        try:
            with Image.open(filepath) as img:
                if hash_type == "phash":
                    return str(imagehash.phash(img))
                elif hash_type == "dhash":
                    return str(imagehash.dhash(img))
                elif hash_type == "ahash":
                    return str(imagehash.average_hash(img))
                else:
                    return str(imagehash.phash(img))
        except Exception:
            return None
    
    def calculate_hash_similarity(self, hash1, hash2):
        """计算两个哈希值的相似度（百分比）"""
        try:
            h1 = imagehash.hex_to_hash(hash1)
            h2 = imagehash.hex_to_hash(hash2)
            # 计算汉明距离
            hamming_distance = h1 - h2
            # 转换为相似度百分比（64位哈希）
            similarity = (64 - hamming_distance) / 64 * 100
            return similarity
        except Exception:
            return 0
    
    def run(self):
        try:
            self.status_signal.emit("正在扫描图片文件...")
            
            # 支持的图片格式
            img_exts = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp')
            
            # 获取所有图片文件
            image_files = []
            for filename in os.listdir(self.folder_path):
                if not self.is_running:
                    return
                    
                file_path = os.path.join(self.folder_path, filename)
                if os.path.isfile(file_path) and filename.lower().endswith(img_exts):
                    # 获取文件大小进行预筛选
                    try:
                        file_size = os.path.getsize(file_path)
                        image_files.append((filename, file_path, file_size))
                    except Exception:
                        continue
            
            total_files = len(image_files)
            if total_files == 0:
                self.complete_signal.emit(False, "未找到图片文件", 0, 0)
                return
            
            self.status_signal.emit(f"找到 {total_files} 个图片文件，开始检测重复...")
            
            # 第一步：按文件大小分组（快速预筛选）
            size_groups = defaultdict(list)
            for filename, filepath, filesize in image_files:
                size_groups[filesize].append((filename, filepath))
            
            # 只处理有多个文件的大小组
            potential_duplicates = []
            for size, files in size_groups.items():
                if len(files) > 1:
                    potential_duplicates.extend(files)
            
            if not potential_duplicates:
                self.complete_signal.emit(True, "未发现重复图片", total_files, 0)
                return
            
            self.status_signal.emit(f"预筛选出 {len(potential_duplicates)} 个可能重复的文件，开始详细对比...")
            
            # 第二步：计算哈希值
            file_hashes = {}
            processed = 0
            
            for filename, filepath in potential_duplicates:
                if not self.is_running:
                    return
                
                if self.comparison_method == "md5":
                    hash_value = self.get_file_hash(filepath)
                else:
                    hash_value = self.get_image_hash(filepath, "phash")
                
                if hash_value:
                    file_hashes[filepath] = {
                        'filename': filename,
                        'hash': hash_value,
                        'size': os.path.getsize(filepath)
                    }
                
                processed += 1
                self.progress_signal.emit(processed, len(potential_duplicates))
                
                # 更新状态
                if processed % 10 == 0:
                    self.status_signal.emit(f"已处理 {processed}/{len(potential_duplicates)} 个文件...")
            
            # 第三步：查找重复
            duplicate_groups = []
            processed_files = set()
            
            if self.comparison_method == "md5":
                # MD5精确匹配
                hash_groups = defaultdict(list)
                for filepath, info in file_hashes.items():
                    hash_groups[info['hash']].append(filepath)
                
                for hash_value, filepaths in hash_groups.items():
                    if len(filepaths) > 1:
                        group = []
                        for fp in filepaths:
                            group.append({
                                'path': fp,
                                'filename': file_hashes[fp]['filename'],
                                'size': file_hashes[fp]['size']
                            })
                        duplicate_groups.append(group)
            else:
                # 感知哈希相似度匹配
                filepaths = list(file_hashes.keys())
                for i, filepath1 in enumerate(filepaths):
                    if filepath1 in processed_files or not self.is_running:
                        continue
                    
                    group = [{'path': filepath1, 
                             'filename': file_hashes[filepath1]['filename'],
                             'size': file_hashes[filepath1]['size']}]
                    
                    for j, filepath2 in enumerate(filepaths[i+1:], i+1):
                        if filepath2 in processed_files:
                            continue
                        
                        similarity = self.calculate_hash_similarity(
                            file_hashes[filepath1]['hash'],
                            file_hashes[filepath2]['hash']
                        )
                        
                        if similarity >= self.similarity_threshold:
                            group.append({
                                'path': filepath2,
                                'filename': file_hashes[filepath2]['filename'],
                                'size': file_hashes[filepath2]['size']
                            })
                            processed_files.add(filepath2)
                    
                    if len(group) > 1:
                        duplicate_groups.append(group)
                        processed_files.add(filepath1)
            
            # 发送结果
            total_duplicates = sum(len(group) - 1 for group in duplicate_groups)  # 每组保留一个
            
            if duplicate_groups:
                self.duplicate_found_signal.emit(duplicate_groups)
                self.complete_signal.emit(True, f"检测完成！发现 {len(duplicate_groups)} 组重复图片，共 {total_duplicates} 个重复文件", 
                                        total_files, total_duplicates)
            else:
                self.complete_signal.emit(True, "未发现重复图片", total_files, 0)
                
        except Exception as e:
            error_msg = f"检测过程中出错: {str(e)}"
            self.status_signal.emit(error_msg)
            self.complete_signal.emit(False, error_msg, 0, 0)

class DuplicateImageRemover(QWidget):
    """重复图删除功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.detection_thread = None
        self.duplicate_groups = []
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能使用高效算法检测和删除重复图片，支持精确匹配和相似度匹配")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 文件夹选择
        folder_layout = QHBoxLayout()
        folder_label = QLabel("选择图片文件夹:")
        self.folder_input = QLineEdit()
        self.folder_input.setReadOnly(True)
        folder_button = QPushButton("浏览...")
        folder_button.clicked.connect(self.select_folder)
        
        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_input)
        folder_layout.addWidget(folder_button)
        main_layout.addLayout(folder_layout)
        
        # 检测设置组
        settings_group = QGroupBox("检测设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 对比方法
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("对比方法:"))
        self.method_combo = QComboBox()
        self.method_combo.addItem("MD5哈希 (精确匹配)", "md5")
        self.method_combo.addItem("感知哈希 (相似度匹配)", "phash")
        self.method_combo.currentTextChanged.connect(self.on_method_changed)
        self.method_combo.currentTextChanged.connect(self.save_config)
        method_layout.addWidget(self.method_combo)
        method_layout.addStretch()
        settings_layout.addLayout(method_layout)
        
        # 相似度阈值（仅感知哈希可用）
        threshold_layout = QHBoxLayout()
        threshold_layout.addWidget(QLabel("相似度阈值:"))
        self.threshold_spinbox = QSpinBox()
        self.threshold_spinbox.setRange(80, 100)
        self.threshold_spinbox.setValue(95)
        self.threshold_spinbox.setSuffix("%")
        self.threshold_spinbox.valueChanged.connect(self.save_config)
        threshold_layout.addWidget(self.threshold_spinbox)
        threshold_layout.addStretch()
        settings_layout.addLayout(threshold_layout)
        self.threshold_layout = threshold_layout
        
        main_layout.addWidget(settings_group)
        
        # 选项设置
        options_layout = QVBoxLayout()
        self.auto_delete_checkbox = QCheckBox("自动删除重复文件（保留每组中的第一个）")
        self.auto_delete_checkbox.stateChanged.connect(self.save_config)
        options_layout.addWidget(self.auto_delete_checkbox)
        
        main_layout.addLayout(options_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%v/%m (%p%)")
        main_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        self.detect_button = QPushButton("开始检测")
        self.detect_button.clicked.connect(self.start_detection)
        self.stop_button = QPushButton("停止检测")
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setEnabled(False)
        
        button_layout.addWidget(self.detect_button)
        button_layout.addWidget(self.stop_button)
        main_layout.addLayout(button_layout)
        
        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(200)
        self.result_text.setReadOnly(True)
        main_layout.addWidget(self.result_text)
        
        # 删除按钮
        self.delete_button = QPushButton("删除选中的重复文件")
        self.delete_button.clicked.connect(self.delete_duplicates)
        self.delete_button.setEnabled(False)
        main_layout.addWidget(self.delete_button)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # 初始化界面状态
        self.on_method_changed()
    
    def on_method_changed(self):
        """对比方法改变时的处理"""
        current_method = self.method_combo.currentData()
        # 只有感知哈希方法才显示相似度阈值
        for i in range(self.threshold_layout.count()):
            widget = self.threshold_layout.itemAt(i).widget()
            if widget:
                widget.setVisible(current_method == "phash")

    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('duplicate_remover', {})

            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.folder_input.setText(last_folder)

            # 设置对比方法
            method = config.get('comparison_method', 'hash')
            for i in range(self.method_combo.count()):
                if self.method_combo.itemData(i) == method:
                    self.method_combo.setCurrentIndex(i)
                    break

            # 设置相似度阈值
            threshold = config.get('similarity_threshold', 95)
            self.threshold_spinbox.setValue(threshold)

            # 设置自动删除
            auto_delete = config.get('auto_delete', False)
            self.auto_delete_checkbox.setChecked(auto_delete)

    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'duplicate_remover' not in self.parent.config:
                self.parent.config['duplicate_remover'] = {}

            # 保存当前设置
            self.parent.config['duplicate_remover']['last_folder'] = self.folder_input.text()

            current_method = self.method_combo.currentData()
            if current_method:
                self.parent.config['duplicate_remover']['comparison_method'] = current_method

            self.parent.config['duplicate_remover']['similarity_threshold'] = self.threshold_spinbox.value()
            self.parent.config['duplicate_remover']['auto_delete'] = self.auto_delete_checkbox.isChecked()

    def select_folder(self):
        """选择图片文件夹"""
        start_dir = self.folder_input.text() if self.folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹", start_dir)
        if folder_path:
            self.folder_input.setText(folder_path)
            logging.info(f"已选择图片文件夹: {folder_path}")
            self.save_config()

    def start_detection(self):
        """开始检测重复图片"""
        folder_path = self.folder_input.text()
        if not folder_path or not os.path.exists(folder_path):
            QMessageBox.warning(self, "警告", "请选择有效的图片文件夹")
            return

        # 获取设置
        method = self.method_combo.currentData() or "md5"
        threshold = self.threshold_spinbox.value()

        # 保存配置
        self.save_config()

        # 重置界面
        self.result_text.clear()
        self.duplicate_groups = []
        self.progress_bar.setValue(0)
        self.status_label.setText("准备开始检测...")

        # 更新按钮状态
        self.detect_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.delete_button.setEnabled(False)

        # 创建并启动检测线程
        self.detection_thread = DuplicateDetectionThread(folder_path, method, threshold)
        self.detection_thread.progress_signal.connect(self.update_progress)
        self.detection_thread.status_signal.connect(self.update_status)
        self.detection_thread.duplicate_found_signal.connect(self.on_duplicates_found)
        self.detection_thread.complete_signal.connect(self.on_detection_complete)
        self.detection_thread.start()

        logging.info(f"开始检测重复图片: 文件夹={folder_path}, 方法={method}, 阈值={threshold}%")

    def stop_detection(self):
        """停止检测"""
        if self.detection_thread and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait()
            logging.info("重复图片检测已停止")

        self.detect_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("检测已停止")

    def update_progress(self, current, total):
        """更新进度条"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)

    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)

    def on_duplicates_found(self, duplicate_groups):
        """处理发现的重复图片"""
        self.duplicate_groups = duplicate_groups

        # 显示结果
        result_text = f"发现 {len(duplicate_groups)} 组重复图片:\n\n"

        for i, group in enumerate(duplicate_groups, 1):
            result_text += f"第 {i} 组 ({len(group)} 个文件):\n"
            for j, file_info in enumerate(group):
                filename = file_info['filename']
                size = file_info['size']
                size_mb = size / (1024 * 1024)
                marker = " [保留]" if j == 0 else " [删除]"
                result_text += f"  • {filename} ({size_mb:.2f} MB){marker}\n"
            result_text += "\n"

        self.result_text.setPlainText(result_text)

        # 如果设置了自动删除，则自动执行删除
        if self.auto_delete_checkbox.isChecked():
            self.delete_duplicates()

    def on_detection_complete(self, success, message, total_files, duplicate_count):
        """检测完成处理"""
        self.detect_button.setEnabled(True)
        self.stop_button.setEnabled(False)

        if success and duplicate_count > 0:
            self.delete_button.setEnabled(True)

        self.status_label.setText(message)

        if success:
            if duplicate_count > 0:
                QMessageBox.information(self, "检测完成", message)
            else:
                QMessageBox.information(self, "检测完成", "未发现重复图片")
        else:
            QMessageBox.critical(self, "检测失败", message)

    def delete_duplicates(self):
        """删除重复文件"""
        if not self.duplicate_groups:
            QMessageBox.warning(self, "警告", "没有发现重复文件")
            return

        # 计算要删除的文件数
        total_to_delete = sum(len(group) - 1 for group in self.duplicate_groups)

        if not self.auto_delete_checkbox.isChecked():
            reply = QMessageBox.question(
                self, "确认删除",
                f"即将删除 {total_to_delete} 个重复文件，每组保留第一个文件。\n\n"
                "删除的文件无法恢复，确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

        # 执行删除
        deleted_count = 0
        failed_count = 0

        for group in self.duplicate_groups:
            # 跳过第一个文件（保留）
            for file_info in group[1:]:
                try:
                    os.remove(file_info['path'])
                    deleted_count += 1
                    logging.info(f"已删除重复文件: {file_info['filename']}")
                except Exception as e:
                    failed_count += 1
                    logging.error(f"删除文件失败 {file_info['filename']}: {str(e)}")

        # 显示结果
        result_msg = f"删除完成！\n成功删除: {deleted_count} 个文件"
        if failed_count > 0:
            result_msg += f"\n删除失败: {failed_count} 个文件"

        QMessageBox.information(self, "删除完成", result_msg)

        # 清空结果
        self.duplicate_groups = []
        self.result_text.clear()
        self.delete_button.setEnabled(False)
        self.status_label.setText("删除完成")

    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.detection_thread and self.detection_thread.isRunning():
            self.detection_thread.stop()
            self.detection_thread.wait()
        event.accept()

# 为了兼容性，保留原来的类名
重复图删除 = DuplicateImageRemover
重复图检测线程 = DuplicateDetectionThread

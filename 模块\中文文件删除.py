#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🗑️ 中文文件删除模块 🗑️
功能: 检测并删除文件名包含中文的文件
"""

import os
import re
import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QLineEdit, QFileDialog, QProgressBar, 
                            QMessageBox, QApplication, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex

class ChineseFileDetector(QThread):
    """中文文件检测和删除线程"""
    progress_signal = pyqtSignal(int)
    status_signal = pyqtSignal(str)
    complete_signal = pyqtSignal(bool, str)
    
    def __init__(self, folder_path, auto_delete=False, max_workers=None):
        super().__init__()
        self.folder_path = folder_path
        self.auto_delete = auto_delete
        self.max_workers = max_workers or min(32, os.cpu_count() + 4)  # 默认工作线程数
        self.is_running = True
        self.mutex = QMutex()
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]')  # 中文字符范围
        self.files_to_process = []
        self.total_files = 0
        self.processed_files = 0
        self.chinese_files_count = 0
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
    
    def is_chinese_filename(self, filename):
        """检查文件名是否包含中文"""
        return bool(self.chinese_pattern.search(filename))
    
    def process_file(self, file_path):
        """处理单个文件"""
        if not self.is_running:
            return None
            
        filename = os.path.basename(file_path)
        if self.is_chinese_filename(filename):
            if self.auto_delete:
                try:
                    os.remove(file_path)
                    return True  # 已删除
                except Exception as e:
                    self.status_signal.emit(f"删除文件失败: {file_path}, 错误: {str(e)}")
                    return False  # 删除失败
            else:
                return True  # 找到但未删除
        return None
        
    def run(self):
        try:
            start_time = time.time()
            self.status_signal.emit("正在扫描文件...")
            
            # 首先收集所有文件
            self.files_to_process = []
            for root, _, files in os.walk(self.folder_path):
                if not self.is_running:
                    self.status_signal.emit("操作已取消")
                    return
                    
                for filename in files:
                    file_path = os.path.join(root, filename)
                    self.files_to_process.append(file_path)
            
            self.total_files = len(self.files_to_process)
            if self.total_files == 0:
                self.complete_signal.emit(False, "所选文件夹中没有文件")
                return
                
            self.status_signal.emit(f"共找到 {self.total_files} 个文件，开始检测中文文件名...")
            self.processed_files = 0
            self.chinese_files_count = 0
            
            # 使用线程池并行处理文件
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.process_file, file_path): file_path 
                    for file_path in self.files_to_process
                }
                
                # 处理结果
                last_update_time = time.time()
                for future in as_completed(future_to_file):
                    if not self.is_running:
                        executor.shutdown(wait=False)
                        self.status_signal.emit("操作已取消")
                        return
                        
                    result = future.result()
                    self.processed_files += 1
                    
                    # 如果找到中文文件
                    if result:
                        self.chinese_files_count += 1
                    
                    # 限制进度更新频率，避免UI卡顿
                    current_time = time.time()
                    if current_time - last_update_time > 0.1 or self.processed_files == self.total_files:
                        progress = int(self.processed_files * 100 / self.total_files)
                        self.progress_signal.emit(progress)
                        last_update_time = current_time
                        # 处理事件，确保UI响应
                        QApplication.processEvents()
            
            # 计算总耗时
            elapsed_time = time.time() - start_time
            
            # 完成消息
            action = "删除" if self.auto_delete else "检测"
            if self.chinese_files_count > 0:
                complete_message = f"完成! 共{action}了 {self.chinese_files_count} 个中文文件名 (共扫描 {self.total_files} 个文件, 耗时 {elapsed_time:.2f} 秒)"
            else:
                complete_message = f"完成! 未发现中文文件名 (共扫描 {self.total_files} 个文件, 耗时 {elapsed_time:.2f} 秒)"
            
            self.status_signal.emit(complete_message)
            self.complete_signal.emit(True, complete_message)
            
        except Exception as e:
            error_message = f"处理过程中出错: {str(e)}"
            self.status_signal.emit(error_message)
            self.complete_signal.emit(False, error_message)

class ChineseFileRemover(QWidget):
    """中文文件删除功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.worker_thread = None
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能可以快速检测并删除文件名中包含中文字符的文件")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(10)
        
        # 文件夹选择
        folder_layout = QHBoxLayout()
        folder_label = QLabel("选择文件夹:")
        self.folder_input = QLineEdit()
        self.folder_input.setReadOnly(True)
        folder_button = QPushButton("浏览...")
        folder_button.clicked.connect(self.select_folder)
        
        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_input)
        folder_layout.addWidget(folder_button)
        form_layout.addLayout(folder_layout)
        
        # 自动删除选项
        self.auto_delete_checkbox = QCheckBox("自动删除中文文件（不勾选则仅检测不删除）")
        self.auto_delete_checkbox.stateChanged.connect(self.save_config)
        form_layout.addWidget(self.auto_delete_checkbox)
        
        main_layout.addLayout(form_layout)
        
        # 添加状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        
        # 添加开始按钮
        self.start_button = QPushButton("开始检测")
        self.start_button.clicked.connect(self.start_detection)
        button_layout.addWidget(self.start_button)
        
        # 添加取消按钮
        self.cancel_button = QPushButton("取消操作")
        self.cancel_button.clicked.connect(self.cancel_operation)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
    
    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('chinese_file_remover', {})
            
            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.folder_input.setText(last_folder)
            
            # 设置是否自动删除
            auto_delete = config.get('auto_delete', False)
            self.auto_delete_checkbox.setChecked(auto_delete)
    
    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'chinese_file_remover' not in self.parent.config:
                self.parent.config['chinese_file_remover'] = {}
            
            # 保存当前设置
            self.parent.config['chinese_file_remover']['last_folder'] = self.folder_input.text()
            self.parent.config['chinese_file_remover']['auto_delete'] = self.auto_delete_checkbox.isChecked()
        
    def select_folder(self):
        """选择文件夹"""
        # 从上次的文件夹开始浏览
        start_dir = self.folder_input.text() if self.folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹", start_dir)
        if folder_path:
            self.folder_input.setText(folder_path)
            logging.info(f"已选择文件夹: {folder_path}")
            # 保存当前文件夹路径
            self.save_config()
    
    def start_detection(self):
        """开始检测中文文件"""
        # 获取输入参数
        folder_path = self.folder_input.text()
        auto_delete = self.auto_delete_checkbox.isChecked()
        
        # 验证输入
        if not folder_path:
            QMessageBox.warning(self, "警告", "请选择文件夹")
            logging.warning("未选择文件夹")
            return
        
        # 保存当前配置
        self.save_config()
        
        # 重置状态和进度条
        action = "删除" if auto_delete else "检测"
        self.status_label.setText(f"准备{action}中文文件...")
        self.progress_bar.setValue(0)
        
        # 禁用开始按钮，启用取消按钮
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        
        # 创建并启动工作线程
        self.worker_thread = ChineseFileDetector(folder_path, auto_delete)
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.status_signal.connect(self.update_status)
        self.worker_thread.complete_signal.connect(self.handle_complete)
        self.worker_thread.start()
        
        action_text = "删除" if auto_delete else "检测"
        logging.info(f"开始{action_text}中文文件: 文件夹={folder_path}, 自动删除={auto_delete}")
    
    def cancel_operation(self):
        """取消操作"""
        if self.worker_thread and self.worker_thread.isRunning():
            # 停止工作线程
            self.worker_thread.stop()
            # 等待线程结束
            self.worker_thread.wait()
            # 更新状态
            self.status_label.setText("操作已取消")
            # 启用开始按钮，禁用取消按钮
            self.start_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            logging.info("中文文件检测已取消")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)
    
    def handle_complete(self, success, message):
        """处理完成回调"""
        # 重置进度条
        self.progress_bar.setValue(100 if success else 0)
        
        # 更新状态
        self.status_label.setText(message)
        
        # 启用开始按钮，禁用取消按钮
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.critical(self, "错误", message)
    
    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.worker_thread.wait()
        event.accept()

# 为了兼容性，提供中文类名
中文文件删除 = ChineseFileRemover 
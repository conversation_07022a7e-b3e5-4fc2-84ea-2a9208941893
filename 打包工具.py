#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
✨ 项目打包工具 ✨
作者: <PERSON> AI
日期: 2023年
描述: 用于将Python项目打包成可执行文件(exe)的工具
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path

def print_colored(text, color_code):
    """打印彩色文本"""
    print(f"\033[{color_code}m{text}\033[0m")

def print_info(text):
    """打印信息"""
    print_colored(f"[信息] {text}", "36")  # 青色

def print_success(text):
    """打印成功信息"""
    print_colored(f"[成功] {text}", "32")  # 绿色

def print_warning(text):
    """打印警告信息"""
    print_colored(f"[警告] {text}", "33")  # 黄色

def print_error(text):
    """打印错误信息"""
    print_colored(f"[错误] {text}", "31")  # 红色

def check_environment():
    """检查环境并安装必要的包"""
    print_info("检查环境...")
    
    # 检查PyInstaller是否已安装
    try:
        import PyInstaller
        print_success("PyInstaller已安装")
    except ImportError:
        print_warning("PyInstaller未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print_success("PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print_error("PyInstaller安装失败，请手动安装后重试")
            sys.exit(1)
    
    # 检查项目依赖
    if os.path.exists("requirements.txt"):
        print_info("安装项目依赖...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print_success("项目依赖安装成功")
        except subprocess.CalledProcessError:
            print_error("项目依赖安装失败，请检查requirements.txt文件")
            sys.exit(1)
    else:
        print_warning("未找到requirements.txt文件，跳过依赖安装")

def cleanup():
    """清理旧的构建文件"""
    print_info("清理旧的构建文件...")
    
    # 删除dist和build目录
    for dir_name in ["dist", "build"]:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print_success(f"已删除{dir_name}目录")
            except Exception as e:
                print_error(f"删除{dir_name}目录失败: {e}")
    
    # 删除spec文件
    for spec_file in Path(".").glob("*.spec"):
        try:
            os.remove(spec_file)
            print_success(f"已删除{spec_file}文件")
        except Exception as e:
            print_error(f"删除{spec_file}文件失败: {e}")

def build_executable():
    """构建可执行文件"""
    print_info("开始构建可执行文件...")
    
    # 图标路径
    icon_path = "G:\\代码处理工具\\Balloon.ico"
    if not os.path.exists(icon_path):
        print_warning(f"图标文件 {icon_path} 不存在，将使用默认图标")
        icon_param = ""
    else:
        icon_param = f"--icon={icon_path}"
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--name=上班工具百宝箱",
        "--noconsole",
        "--onefile",
        icon_param,
        "--add-data=config.json;.",
        "--add-data=key.vdf;.",
        "--add-data=模块;模块",
        "main.py"
    ]
    
    # 过滤掉空参数
    cmd = [item for item in cmd if item]
    
    print_info(f"执行命令: {' '.join(cmd)}")
    
    try:
        subprocess.check_call(cmd)
        print_success("构建成功!")
    except subprocess.CalledProcessError as e:
        print_error(f"构建失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    print_colored("=" * 60, "35")
    print_colored("            项目打包工具 - 上班工具百宝箱                ", "35")
    print_colored("=" * 60, "35")
    
    start_time = time.time()
    
    # 检查环境
    check_environment()
    
    # 清理旧的构建文件
    cleanup()
    
    # 构建可执行文件
    build_executable()
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    print_colored(f"打包完成! 总耗时: {elapsed_time:.2f}秒", "35")
    print_colored("=" * 60, "35")
    
    # 打开输出目录
    dist_dir = os.path.join(os.getcwd(), "dist")
    if os.path.exists(dist_dir):
        print_info(f"打包文件位于: {dist_dir}")
        try:
            os.startfile(dist_dir)
        except AttributeError:
            # 在非Windows系统上使用其他方法打开文件夹
            import subprocess
            subprocess.Popen(["xdg-open", dist_dir])
    
    # 等待用户按键退出
    input("按Enter键退出...")

if __name__ == "__main__":
    main() 
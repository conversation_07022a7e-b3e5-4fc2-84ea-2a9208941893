#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🖼️ 图片加白底模块 🖼️
功能: 为透明背景的图片添加纯白色背景
"""

import os
import logging
import time
from PIL import Image
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QLineEdit, QFileDialog, QProgressBar, 
                            QMessageBox, QCheckBox, QApplication)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMutex, QWaitCondition

class ImageProcessThread(QThread):
    """图片处理线程"""
    progress_signal = pyqtSignal(int)
    status_signal = pyqtSignal(str)
    complete_signal = pyqtSignal(bool, str)
    
    def __init__(self, image_folder, keep_original=False):
        super().__init__()
        self.image_folder = image_folder
        self.keep_original = keep_original
        self.is_running = True
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
    def stop(self):
        """停止线程"""
        self.mutex.lock()
        self.is_running = False
        self.mutex.unlock()
        self.condition.wakeAll()
        
    def run(self):
        try:
            # 获取文件夹中的所有图片文件
            self.status_signal.emit("正在扫描图片文件...")
            image_files = []
            supported_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp']
            
            # 检查是否被中断
            if not self.is_running:
                return
                
            for filename in os.listdir(self.image_folder):
                file_path = os.path.join(self.image_folder, filename)
                if os.path.isfile(file_path) and os.path.splitext(filename)[1].lower() in supported_formats:
                    image_files.append(filename)
            
            # 如果没有图片文件，则返回错误
            if not image_files:
                self.complete_signal.emit(False, "所选文件夹中没有支持的图片文件")
                return
            
            # 处理每个图片文件
            processed_files = 0
            processed_transparent_images = 0
            total_files = len(image_files)
            last_update_time = time.time()
            
            self.status_signal.emit(f"开始处理 {total_files} 个图片文件...")
            
            for filename in image_files:
                # 检查是否被中断
                if not self.is_running:
                    self.status_signal.emit("处理已取消")
                    return
                    
                file_path = os.path.join(self.image_folder, filename)
                
                try:
                    # 打开图片
                    img = Image.open(file_path)
                    
                    # 检查图片是否有透明通道
                    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                        # 创建白色背景
                        white_bg = Image.new('RGBA', img.size, (255, 255, 255, 255))
                        
                        # 将原图覆盖到白色背景上
                        if img.mode == 'RGBA':
                            white_bg.paste(img, (0, 0), img)
                        else:
                            white_bg.paste(img, (0, 0))
                        
                        # 转换为RGB模式（去除透明通道）
                        processed_img = white_bg.convert('RGB')
                        
                        # 如果需要保留原图，则创建新文件名
                        if self.keep_original:
                            filename_no_ext, ext = os.path.splitext(filename)
                            new_filename = f"{filename_no_ext}_白底{ext}"
                            new_file_path = os.path.join(self.image_folder, new_filename)
                            processed_img.save(new_file_path)
                        else:
                            # 保存处理后的图片（覆盖原图）
                            processed_img.save(file_path)
                        
                        processed_transparent_images += 1
                        self.status_signal.emit(f"已处理图片: {filename}")
                    
                    # 关闭图片
                    img.close()
                    
                except Exception as e:
                    self.status_signal.emit(f"处理图片 {filename} 时出错: {str(e)}")
                
                # 更新进度
                processed_files += 1
                
                # 限制进度更新频率，避免UI卡顿
                current_time = time.time()
                if current_time - last_update_time > 0.1 or processed_files == total_files:
                    progress_percent = int(processed_files * 100 / total_files)
                    self.progress_signal.emit(progress_percent)
                    last_update_time = current_time
                    # 处理事件，确保UI响应
                    QApplication.processEvents()
            
            complete_message = f"图片处理完成！共处理 {processed_files} 个图片文件，其中 {processed_transparent_images} 个透明图片已添加白色背景。"
            self.status_signal.emit(complete_message)
            self.complete_signal.emit(True, complete_message)
            
        except Exception as e:
            error_message = f"图片处理过程中出错: {str(e)}"
            self.status_signal.emit(error_message)
            self.complete_signal.emit(False, error_message)

class AddWhiteBackground(QWidget):
    """图片加白底功能界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.worker_thread = None
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化界面元素"""
        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 添加说明标签
        description_label = QLabel("此功能可以为透明背景的图片添加纯白色背景")
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)
        
        # 创建表单布局
        form_layout = QVBoxLayout()
        form_layout.setSpacing(10)
        
        # 图片文件夹选择
        image_folder_layout = QHBoxLayout()
        image_folder_label = QLabel("选择图片文件夹:")
        self.image_folder_input = QLineEdit()
        self.image_folder_input.setReadOnly(True)
        image_folder_button = QPushButton("浏览...")
        image_folder_button.clicked.connect(self.select_image_folder)
        
        image_folder_layout.addWidget(image_folder_label)
        image_folder_layout.addWidget(self.image_folder_input)
        image_folder_layout.addWidget(image_folder_button)
        form_layout.addLayout(image_folder_layout)
        
        # 保留原图选项
        self.keep_original_checkbox = QCheckBox("保留原图（处理后的图片将保存为新文件）")
        self.keep_original_checkbox.stateChanged.connect(self.save_config)
        form_layout.addWidget(self.keep_original_checkbox)
        
        main_layout.addLayout(form_layout)
        
        # 添加状态标签
        self.status_label = QLabel("")
        self.status_label.setWordWrap(True)
        main_layout.addWidget(self.status_label)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        
        # 添加开始按钮
        self.start_button = QPushButton("开始处理")
        self.start_button.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_button)
        
        # 添加取消按钮
        self.cancel_button = QPushButton("取消处理")
        self.cancel_button.clicked.connect(self.cancel_processing)
        self.cancel_button.setEnabled(False)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        
        # 添加弹性空间
        main_layout.addStretch()
        
        self.setLayout(main_layout)
    
    def load_config(self):
        """从主窗口加载配置"""
        if hasattr(self.parent, 'config') and self.parent.config:
            config = self.parent.config.get('add_white_bg', {})
            
            # 设置上次使用的文件夹
            last_folder = config.get('last_folder', '')
            if last_folder and os.path.exists(last_folder):
                self.image_folder_input.setText(last_folder)
            
            # 设置是否保留原图
            keep_original = config.get('keep_original', False)
            self.keep_original_checkbox.setChecked(keep_original)
    
    def save_config(self):
        """保存配置到主窗口"""
        if hasattr(self.parent, 'config'):
            if 'add_white_bg' not in self.parent.config:
                self.parent.config['add_white_bg'] = {}
            
            # 保存当前设置
            self.parent.config['add_white_bg']['last_folder'] = self.image_folder_input.text()
            self.parent.config['add_white_bg']['keep_original'] = self.keep_original_checkbox.isChecked()
        
    def select_image_folder(self):
        """选择图片文件夹"""
        # 从上次的文件夹开始浏览
        start_dir = self.image_folder_input.text() if self.image_folder_input.text() else ""
        folder_path = QFileDialog.getExistingDirectory(self, "选择图片文件夹", start_dir)
        if folder_path:
            self.image_folder_input.setText(folder_path)
            logging.info(f"已选择图片文件夹: {folder_path}")
            # 保存当前文件夹路径
            self.save_config()
    
    def start_processing(self):
        """开始图片处理"""
        # 获取输入参数
        image_folder = self.image_folder_input.text()
        keep_original = self.keep_original_checkbox.isChecked()
        
        # 验证输入
        if not image_folder:
            QMessageBox.warning(self, "警告", "请选择图片文件夹")
            logging.warning("未选择图片文件夹")
            return
        
        # 保存当前配置
        self.save_config()
        
        # 重置状态和进度条
        self.status_label.setText("准备处理图片...")
        self.progress_bar.setValue(0)
        
        # 禁用开始按钮，启用取消按钮
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)
        
        # 创建并启动工作线程
        self.worker_thread = ImageProcessThread(image_folder, keep_original)
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.status_signal.connect(self.update_status)
        self.worker_thread.complete_signal.connect(self.handle_complete)
        self.worker_thread.start()
        
        logging.info(f"开始处理图片: 图片文件夹={image_folder}, 保留原图={keep_original}")
    
    def cancel_processing(self):
        """取消图片处理"""
        if self.worker_thread and self.worker_thread.isRunning():
            # 停止工作线程
            self.worker_thread.stop()
            # 等待线程结束
            self.worker_thread.wait()
            # 更新状态
            self.status_label.setText("处理已取消")
            # 启用开始按钮，禁用取消按钮
            self.start_button.setEnabled(True)
            self.cancel_button.setEnabled(False)
            logging.info("图片处理已取消")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态标签"""
        self.status_label.setText(message)
        logging.info(message)
    
    def handle_complete(self, success, message):
        """处理完成回调"""
        # 重置进度条
        self.progress_bar.setValue(100 if success else 0)
        
        # 更新状态
        self.status_label.setText(message)
        
        # 启用开始按钮，禁用取消按钮
        self.start_button.setEnabled(True)
        self.cancel_button.setEnabled(False)
        
        # 显示结果消息
        if success:
            QMessageBox.information(self, "完成", message)
        else:
            QMessageBox.critical(self, "错误", message)
    
    def closeEvent(self, event):
        """关闭窗口时停止线程"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.stop()
            self.worker_thread.wait()
        event.accept()

# 为了兼容性，保留原来的类名
图片加白底 = AddWhiteBackground
图片处理线程 = ImageProcessThread 